import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/smart_composer_controller.dart';
import '../controllers/novel_controller.dart';
import '../models/smart_composer_models.dart';
import '../models/novel.dart';
import '../services/novel_file_manager.dart';
import '../screens/novel_detail_screen.dart';


/// Smart Composer 演示界面
/// 展示集成的 AI 写作助手功能
class SmartComposerDemoScreen extends StatefulWidget {
  const SmartComposerDemoScreen({super.key});

  @override
  State<SmartComposerDemoScreen> createState() => _SmartComposerDemoScreenState();
}

class _SmartComposerDemoScreenState extends State<SmartComposerDemoScreen> {
  final SmartComposerController _smartComposerController = Get.find<SmartComposerController>();
  final NovelController _novelController = Get.find<NovelController>();
  final NovelFileManager _fileManager = NovelFileManager();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Smart Composer 演示'),
        actions: [],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeCard(),
            const SizedBox(height: 16),
            _buildFeaturesCard(),
            const SizedBox(height: 16),
            _buildQuickActionsCard(),
            const SizedBox(height: 16),
            _buildNovelsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.smart_toy,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '欢迎使用 Smart Composer',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Smart Composer 是一个强大的 AI 写作助手，类似于 Cursor 的体验。'
              '它会自动使用您在设置中配置的 AI 模型，支持在编辑时右侧显示聊天框，'
              '提供实时的写作建议和内容优化。',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final defaultModel = _smartComposerController.defaultModel;
              final isConfigured = defaultModel != null && 
                  _smartComposerController.isProviderConfigured(defaultModel.providerId);
              
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isConfigured 
                      ? Colors.green.withOpacity(0.1)
                      : Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isConfigured ? Colors.green : Colors.orange,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      isConfigured ? Icons.check_circle : Icons.warning,
                      color: isConfigured ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isConfigured 
                            ? '当前模型：${defaultModel.id}'
                            : '请先配置 AI 模型',
                        style: TextStyle(
                          color: isConfigured ? Colors.green[700] : Colors.orange[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    if (!isConfigured)
                      TextButton(
                        onPressed: () => Get.toNamed('/settings'),
                        child: const Text('去设置'),
                      ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '主要功能',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem(
              Icons.auto_fix_high,
              'AI 编辑器',
              '类似 Cursor 的体验：左侧编辑器，右侧 AI 聊天框，实时写作建议',
            ),
            _buildFeatureItem(
              Icons.chat,
              '智能对话',
              '与 AI 进行自然语言对话，获取写作建议和创意灵感',
            ),
            _buildFeatureItem(
              Icons.sync,
              '自动配置',
              '自动使用您在设置中配置的 API 密钥，无需重复配置',
            ),
            _buildFeatureItem(
              Icons.folder,
              '文件夹管理',
              '将小说组织为文件夹结构，每个章节独立存储为 Markdown 文件',
            ),
            _buildFeatureItem(
              Icons.model_training,
              '多模型支持',
              '支持 OpenAI、Claude、Gemini、DeepSeek 等多种 AI 模型',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [

                _buildActionButton(
                  '创建演示小说',
                  Icons.book,
                  () => _createDemoNovel(),
                ),
                _buildActionButton(
                  '查看书库',
                  Icons.library_books,
                  () => Get.toNamed('/library'),
                ),
                _buildActionButton(
                  '快速配置AI',
                  Icons.flash_on,
                  () => Get.toNamed('/quick_ai_setup'),
                ),

              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildNovelsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '我的小说',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Obx(() {
              if (_novelController.novels.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: Column(
                      children: [
                        Icon(Icons.book, size: 48, color: Colors.grey),
                        SizedBox(height: 8),
                        Text(
                          '还没有小说',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return Column(
                children: _novelController.novels.take(3).map((novel) {
                  return ListTile(
                    leading: Icon(
                      novel.useFileSystem ? Icons.folder : Icons.book,
                      color: novel.useFileSystem ? Colors.amber[700] : Colors.blue[700],
                    ),
                    title: Text(novel.title),
                    subtitle: Text('${novel.chapters.length}章 • ${novel.wordCount}字'),
                    trailing: IconButton(
                      icon: const Icon(Icons.auto_fix_high),
                      tooltip: '岱宗AI辅助助手',
                      onPressed: () => _openDaizongAIAssistant(novel),
                    ),
                    onTap: () => Get.to(() => NovelDetailScreen(novel: novel)),
                  );
                }).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }



  void _createDemoNovel() {
    final demoNovel = Novel(
      title: 'Smart Composer 演示小说',
      genre: '科幻',
      outline: '''这是一个关于AI写作助手的科幻小说。

主要情节：
1. 发现神秘的AI写作助手
2. 探索AI的创作能力
3. 人机协作创作的奇妙体验
4. 对未来写作方式的思考''',
      content: '',
      chapters: [
        Chapter(
          number: 1,
          title: '神秘的助手',
          content: '''在一个普通的夜晚，作家李明打开了他的写作软件。突然，屏幕上出现了一个神秘的对话框：

"您好，我是Smart Composer，您的AI写作助手。我可以帮助您创作出精彩的故事。"

李明惊讶地看着这个突然出现的助手，心中充满了好奇...''',
        ),
        Chapter(
          number: 2,
          title: '初次合作',
          content: '''李明决定尝试与这个AI助手合作。他输入了一个简单的故事开头，AI立即给出了精彩的续写建议。

"这个情节可以这样发展..."AI助手说道。

李明发现，这个助手不仅能够理解他的创作意图，还能提供富有创意的建议...''',
        ),
      ],
      createdAt: DateTime.now(),
      useFileSystem: false,
    );

    _novelController.saveNovel(demoNovel);
    Get.snackbar('成功', '演示小说已创建');
  }



  void _openDaizongAIAssistant(Novel novel) {
    // AI助手功能已被移除
    Get.snackbar('提示', 'AI助手功能已被移除');
  }


}
