import 'dart:io';
import 'package:file_selector/file_selector.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:novel_app/models/knowledge_document.dart';
import 'package:novel_app/services/payment_service.dart';
import 'package:novel_app/services/auth_service.dart';

class KnowledgeBaseController extends GetxController {
  static const _boxName = 'knowledge_base';
  late final Box<dynamic> _box;

  final documents = <KnowledgeDocument>[].obs;
  final categories = <String>['未分类', '专业知识', '写作技巧'].obs;
  final selectedDocIds = <String>{}.obs;

  // 是否使用知识库
  final RxBool useKnowledgeBase = false.obs;

  // 是否处于多选模式
  final RxBool isMultiSelectMode = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeBox();
  }

  Future<void> _initializeBox() async {
    _box = await Hive.openBox(_boxName);
    await _migrateData();
    _loadData();
    _loadSettings();
  }

  // 数据迁移
  Future<void> _migrateData() async {
    final version = _box.get('data_version', defaultValue: 0);
    if (version < 2) {
      // 版本2：添加isVisible、isReadOnly、tags、isDefault字段
      print('[KnowledgeBase] 执行数据迁移到版本2');
      await _box.delete('documents');
      await _box.put('data_version', 2);
      print('[KnowledgeBase] 数据迁移完成');
    }
  }

  // 加载数据
  Future<void> _loadData() async {
    final savedDocs = _box.get('documents');
    if (savedDocs != null) {
      final docs = (savedDocs as List)
          .map((e) => KnowledgeDocument.fromJson(Map<String, dynamic>.from(e)))
          .toList();
      documents.assignAll(docs);
    }

    final savedCategories = _box.get('categories');
    if (savedCategories != null) {
      final loadedCategories = List<String>.from(savedCategories);
      // 确保默认分类始终存在
      final defaultCategories = ['未分类', '专业知识', '写作技巧'];
      for (final category in defaultCategories) {
        if (!loadedCategories.contains(category)) {
          loadedCategories.add(category);
        }
      }
      categories.assignAll(loadedCategories);
    } else {
      // 如果没有保存的分类，使用默认分类
      categories.assignAll(['未分类', '专业知识', '写作技巧']);
    }

    // 初始化默认的写作技巧文档
    await _initializeDefaultDocuments();
  }

  void _loadSettings() {
    useKnowledgeBase.value =
        _box.get('use_knowledge_base', defaultValue: false);
    final saved = _box.get('selected_doc_ids');
    if (saved != null) {
      selectedDocIds.addAll(List<String>.from(saved));
    }
  }

  // 初始化默认文档
  Future<void> _initializeDefaultDocuments() async {
    // 检查是否已经存在爽文期待感文档
    final existingDoc = documents.firstWhereOrNull(
      (doc) => doc.title == '爽文期待感营造技巧' && doc.category == '写作技巧',
    );

    if (existingDoc == null) {
      // 创建默认的爽文期待感文档
      final defaultDoc = KnowledgeDocument(
        title: '爽文期待感营造技巧',
        content: '''期待感营造技巧：
**【展现价值】期待感构建：**
1. 展示角色的能力（个性）- 让读者了解角色的独特价值
2. 提供发挥能力的空间 - 创造能力可以发挥的背景环境
3. 展示对这个能力的迫切需求 - 有人急需这种能力
4. 埋没角色的价值 - 角色被轻视、压迫、冷落、质疑或无知对待
当读者看到有价值的东西没能被正确对待时，会产生强烈期待，希望看到价值得到展现，过错方受到惩罚。

**【矛盾冲突】期待感构建：**
1. 一个能力 - 角色具备某种能力
2. 一个不恰当的规则 - 限制或违背能力发挥的规则
3. 一个欲望 - 角色的内在需求或目标
4. 欲望带来的压力 - 外在阻碍或威胁
构建两个相互影响的矛盾：能力vs规则，欲望vs压力。让读者期待角色打破规则，实现欲望。

**应用原则：**
- 能力+条件=满足需求，通过设置不同条件让能力发挥价值
- 价值被埋没时读者会产生期待，希望看到价值展现
- 矛盾不是孤立的，要构建相互依存、相互影响的矛盾关系
- 通过信息差、反套路、张力制造等方法强化期待感''',
        category: '写作技巧',
      );

      await addDocument(defaultDoc);
      print('[KnowledgeBase] 已添加默认的爽文期待感营造技巧文档');
    }

    // 检查是否已经存在AI写作优化技巧文档
    final existingAIDoc = documents.firstWhereOrNull(
      (doc) => doc.title == 'AI写作优化技巧' && doc.category == '写作技巧',
    );

    if (existingAIDoc == null) {
      // 创建新的AI写作优化技巧文档
      final aiWritingDoc = KnowledgeDocument(
        title: 'AI写作优化技巧',
        content: '''在创作小说过程中，你的首要任务是提升文本的突发性（burstiness）。请剧烈地变化句子长度，将冗长、复杂的描述性长句与简短、有力、甚至破碎的动作短句混合在一起。同时，确保段落长度也异构化，有些段落可以洋洋洒洒，进行细致描写，而另一些段落可能仅仅包含一行对话或一个动作。

在创作过程中，提升文本的困惑度（perplexity）。避免使用过于常见或平庸的词汇，但同时也要避免不自然的学术或书面语。多使用更自然、更口语化的表达。几乎完全使用主动语态来增强文本的直接性和力量感。为文本注入一种自信而略带傲慢的语调。在适当的地方，可以融入一些符合人物身份的俗语或俚语。

严格避免使用以下词汇和短语：精心、深入探讨、领域、在当今的数字时代、不仅如此、踏上…的征程、错综复杂、可以说…。解构所有常见的AI句式，特别是三联排比句（例如：它不仅X，还Y，更Z）。避免任何听起来过于通用、冗余或公式化的句子。

为文本注入人性化的元素。这并非指添加语法错误，而是指用一种明确的、主观的视角进行写作。引用具体的感官细节（如气味、质感、声音）。允许叙述者的思绪偶尔打断叙事的流畅性，形成自然的内心独白。最终目标是让文本听起来像一个真实的人写的，而不是一台追求完美的机器。''',
        category: '写作技巧',
        isVisible: true,   // 设置为可见（在列表中显示）
        isReadOnly: true,  // 设置为只读（不可查看和编辑内容）
        tags: ['官方'],    // 添加官方标签
        isDefault: true,   // 设置为默认分类
      );

      await addDocument(aiWritingDoc);
      print('[KnowledgeBase] 已添加默认的AI写作优化技巧文档');
    }
  }

  Future<void> saveSettings() async {
    await _box.put('use_knowledge_base', useKnowledgeBase.value);
    await _box.put('selected_doc_ids', selectedDocIds.toList());
  }

  // 添加分类
  Future<void> addCategory(String category) async {
    if (!categories.contains(category)) {
      categories.add(category);
      await _box.put('categories', categories.toList());
    }
  }

  // 添加文档
  Future<bool> addDocument(KnowledgeDocument doc) async {
    // 检查知识库文档数量限制
    if (!_canAddDocument()) {
      return false;
    }

    documents.add(doc);
    await _box.put('documents', documents.map((d) => d.toJson()).toList());
    return true;
  }

  /// 检查是否可以添加文档
  bool _canAddDocument() {
    // 取消所有限制，始终允许添加文档
    return true;
  }

  // 更新文档
  Future<void> updateDocument(KnowledgeDocument doc) async {
    final index = documents.indexWhere((d) => d.id == doc.id);
    if (index != -1) {
      documents[index] = doc;
      await _box.put('documents', documents.map((d) => d.toJson()).toList());
    }
  }

  // 删除文档
  Future<void> deleteDocument(String id) async {
    documents.removeWhere((doc) => doc.id == id);
    selectedDocIds.remove(id); // 同时移除选中状态
    await _box.put('documents', documents.map((d) => d.toJson()).toList());
    await saveSettings();
  }

  // 切换多选模式
  void toggleMultiSelectMode() {
    isMultiSelectMode.value = !isMultiSelectMode.value;
    if (!isMultiSelectMode.value) {
      clearSelection();
    }
  }

  // 切换文档选择状态
  void toggleDocumentSelection(String id) {
    if (selectedDocIds.contains(id)) {
      selectedDocIds.remove(id);
    } else {
      selectedDocIds.add(id);
    }
    saveSettings();
  }

  // 选择所有文档
  void selectAllDocuments() {
    selectedDocIds.addAll(documents.map((doc) => doc.id));
    saveSettings();
  }

  // 取消选择所有文档
  void deselectAllDocuments() {
    selectedDocIds.clear();
    saveSettings();
  }

  // 获取选中的文档
  List<KnowledgeDocument> getSelectedDocuments() {
    return documents.where((doc) => selectedDocIds.contains(doc.id)).toList();
  }

  // 清除所有选中状态
  void clearSelection() {
    selectedDocIds.clear();
    isMultiSelectMode.value = false;
    saveSettings();
  }

  // 构建包含知识库的提示词
  String buildPromptWithKnowledge(String userPrompt) {
    if (!useKnowledgeBase.value || selectedDocIds.isEmpty) return userPrompt;

    final selectedDocs =
        documents.where((doc) => selectedDocIds.contains(doc.id)).toList();

    if (selectedDocs.isEmpty) return userPrompt;

    String knowledgeContext = "请根据以下知识库内容生成符合设定的小说：\n\n";

    // 按分类组织知识内容
    Map<String, List<KnowledgeDocument>> docsByCategory = {};
    for (var doc in selectedDocs) {
      if (!docsByCategory.containsKey(doc.category)) {
        docsByCategory[doc.category] = [];
      }
      docsByCategory[doc.category]!.add(doc);
    }

    // 按分类添加知识内容，使用不同的提示词
    docsByCategory.forEach((category, docs) {
      if (category == '专业知识') {
        knowledgeContext += "# 专业知识参考\n";
        knowledgeContext += "以下是相关的专业知识，请在创作中确保内容的专业性和准确性，合理融入这些知识：\n\n";
        for (var doc in docs) {
          knowledgeContext += "## ${doc.title}\n${doc.content}\n\n";
        }
      } else if (category == '写作技巧') {
        knowledgeContext += "# 写作技巧要求\n";
        knowledgeContext += "请严格按照以下写作技巧和要求进行创作，确保作品质量和吸引力：\n\n";
        for (var doc in docs) {
          knowledgeContext += "## ${doc.title}\n${doc.content}\n\n";
        }
      } else {
        // 其他分类使用通用格式
        knowledgeContext += "# $category\n";
        for (var doc in docs) {
          knowledgeContext += "## ${doc.title}\n${doc.content}\n\n";
        }
      }
    });

    knowledgeContext += "---\n\n用户要求：\n$userPrompt";

    return knowledgeContext;
  }

  // 选择文件
  Future<File?> pickFile() async {
    try {
      // 定义允许的文件类型
      final typeGroup = XTypeGroup(
        label: '支持的文件格式',
        extensions: ['txt', 'md', 'pdf', 'docx'],
      );

      // 打开文件选择器
      final XFile? xFile = await openFile(
        acceptedTypeGroups: [typeGroup],
        confirmButtonText: '选择',
      );

      if (xFile != null) {
        return File(xFile.path);
      }
    } catch (e) {
      print('选择文件时出错: $e');
    }
    return null;
  }

  // 处理文件内容
  Future<String> processFileContent(File file) async {
    try {
      final extension = path.extension(file.path).toLowerCase();
      switch (extension) {
        case '.txt':
        case '.md':
          return await file.readAsString();
        case '.pdf':
          // TODO: 实现PDF文件处理
          return 'PDF文件内容待处理';
        case '.docx':
          // TODO: 实现DOCX文件处理
          return 'DOCX文件内容待处理';
        default:
          return '不支持的文件格式';
      }
    } catch (e) {
      print('处理文件内容时出错: $e');
      return '文件处理失败';
    }
  }

  // 上传文件
  Future<KnowledgeDocument?> uploadFile({
    required String title,
    required String category,
    String? initialContent,
  }) async {
    try {
      final file = await pickFile();
      if (file == null) return null;

      final content = await processFileContent(file);
      final doc = KnowledgeDocument(
        title: title,
        content:
            initialContent != null ? '$initialContent\n\n$content' : content,
        category: category,
        filePath: file.path,
        fileType: path.extension(file.path).substring(1),
      );

      await addDocument(doc);
      return doc;
    } catch (e) {
      print('上传文件时出错: $e');
      return null;
    }
  }

  /// 获取所有选中文档的内容，用于LangChain处理
  String getSelectedDocsContent() {
    if (selectedDocIds.isEmpty) return '';

    final selectedDocs = documents.where((doc) => selectedDocIds.contains(doc.id)).toList();
    if (selectedDocs.isEmpty) return '';

    final buffer = StringBuffer();

    // 按分类组织文档
    Map<String, List<KnowledgeDocument>> docsByCategory = {};
    for (var doc in selectedDocs) {
      if (!docsByCategory.containsKey(doc.category)) {
        docsByCategory[doc.category] = [];
      }
      docsByCategory[doc.category]!.add(doc);
    }

    // 按分类添加内容，使用不同的提示词
    docsByCategory.forEach((category, docs) {
      if (category == '专业知识') {
        buffer.writeln('# 专业知识参考');
        buffer.writeln('以下是相关的专业知识，请在创作中确保内容的专业性和准确性，合理融入这些知识：\n');
        for (var doc in docs) {
          buffer.writeln('## ${doc.title}');
          buffer.writeln(doc.content);
          buffer.writeln();
        }
      } else if (category == '写作技巧') {
        buffer.writeln('# 写作技巧要求');
        buffer.writeln('请严格按照以下写作技巧和要求进行创作，确保作品质量和吸引力：\n');
        for (var doc in docs) {
          buffer.writeln('## ${doc.title}');
          buffer.writeln(doc.content);
          buffer.writeln();
        }
      } else {
        // 其他分类使用通用格式
        buffer.writeln('# $category');
        for (var doc in docs) {
          buffer.writeln('## ${doc.title}');
          buffer.writeln(doc.content);
          buffer.writeln();
        }
      }
      buffer.writeln('---\n');
    });

    return buffer.toString();
  }
}
