{"buildFiles": ["D:\\element\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\vs code\\novel\\novel2\\android\\app\\.cxx\\RelWithDebInfo\\192w3u64\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\vs code\\novel\\novel2\\android\\app\\.cxx\\RelWithDebInfo\\192w3u64\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}